{"name": "vinehoo-cli", "version": "1.0.1", "description": "酒云网中台系统-平台架构模版", "author": "<PERSON><PERSON><PERSON>", "private": false, "scripts": {"s": "cross-env NODE_ENV=development vue-cli-service serve ", "serve": "cross-env NODE_ENV=development vue-cli-service serve ", "dev": "cross-env NODE_ENV=staging vue-cli-service serve ", "production": "cross-env NODE_ENV=production vue-cli-service serve", "build": "cross-env NODE_ENV=development vue-cli-service build", "build:dev": "cross-env NODE_ENV=staging vue-cli-service build", "build:prod": "cross-env NODE_ENV=production vue-cli-service build"}, "dependencies": {"ali-oss": "^6.16.0", "axios": "^0.19.2", "bootstrap-vue": "^2.21.2", "core-js": "^3.11.3", "cross-env": "^7.0.3", "element-ui": "^2.15.1", "js-cookie": "^2.2.1", "node-sass": "^4.14.1", "sass-loader": "^8.0.0", "vinehoo-v3-api-sign": "^1.3.3", "vos-oss": "^1.6.4", "vue": "^2.6.12", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.12", "@vue/cli-service": "^4.5.12", "compression-webpack-plugin": "^1.1.12", "vue-template-compiler": "^2.6.12"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {"curly": ["error", "multi-line"], "semi": "error", "comma-spacing": ["error", {"before": false, "after": true}], "no-multiple-empty-lines": ["error", {"max": 1, "maxEOF": 1}], "brace-style": ["error", "1tbs", {"allowSingleLine": true}]}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}