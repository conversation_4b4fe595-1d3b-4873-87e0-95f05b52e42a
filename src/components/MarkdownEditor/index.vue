<template>
    <div class="markdown-editor">
        <!-- 工具栏 -->
        <div class="editor-toolbar">
            <div class="toolbar-group">
                <el-button
                    v-for="tool in toolbarItems"
                    :key="tool.type"
                    size="mini"
                    :icon="tool.icon"
                    :title="tool.title"
                    @click="insertModule(tool.type)"
                >
                    {{ tool.label }}
                </el-button>
            </div>
        </div>

        <!-- 编辑区域 -->
        <div class="editor-container">
            <div class="editor-content">
                <el-input
                    ref="editor"
                    v-model="content"
                    type="textarea"
                    :rows="20"
                    placeholder="请输入内容..."
                    @input="handleInput"
                />
            </div>

            <!-- 预览区域 -->
            <div class="editor-preview" v-if="showPreview">
                <div class="preview-content" v-html="previewHtml"></div>
            </div>
        </div>

        <!-- 模块配置弹窗 -->
        <el-dialog
            :title="currentDialog.title"
            :visible.sync="dialogVisible"
            width="600px"
            @close="resetDialog"
        >
            <!-- 标题配置 -->
            <div v-if="currentDialog.type === 'title'">
                <el-form :model="titleForm" label-width="80px">
                    <el-form-item label="标题内容">
                        <el-input
                            v-model="titleForm.content"
                            placeholder="请输入标题内容"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="标题级别">
                        <el-select v-model="titleForm.level">
                            <el-option label="一级标题" value="1"></el-option>
                            <el-option label="二级标题" value="2"></el-option>
                            <el-option label="三级标题" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 子标题配置 -->
            <div v-if="currentDialog.type === 'subtitle'">
                <el-form :model="subtitleForm" label-width="80px">
                    <el-form-item label="子标题内容">
                        <el-input
                            v-model="subtitleForm.content"
                            placeholder="请输入子标题内容"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 日期配置 -->
            <div v-if="currentDialog.type === 'date'">
                <el-form :model="dateForm" label-width="80px">
                    <el-form-item label="选择日期">
                        <el-date-picker
                            v-model="dateForm.date"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="显示格式">
                        <el-select v-model="dateForm.format">
                            <el-option
                                label="2025-01-01"
                                value="YYYY-MM-DD"
                            ></el-option>
                            <el-option
                                label="2025年1月1日"
                                value="YYYY年MM月DD日"
                            ></el-option>
                            <el-option
                                label="January 1, 2025"
                                value="MMMM D, YYYY"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 文本框配置 -->
            <div v-if="currentDialog.type === 'textbox'">
                <el-form :model="textboxForm" label-width="80px">
                    <el-form-item label="文本内容">
                        <el-input
                            v-model="textboxForm.content"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入文本内容"
                        >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 图片配置 -->
            <div v-if="currentDialog.type === 'image'">
                <el-form :model="imageForm" label-width="80px">
                    <el-form-item label="上传图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="imageDir"
                            :file-list="imageForm.fileList"
                            :limit="1"
                            @change="handleImageChange"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="图片描述">
                        <el-input
                            v-model="imageForm.alt"
                            placeholder="请输入图片描述"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="图片标注">
                        <el-input
                            v-model="imageForm.caption"
                            placeholder="请输入图片标注"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 列表配置 -->
            <div v-if="currentDialog.type === 'list'">
                <el-form :model="listForm" label-width="80px">
                    <el-form-item label="列表类型">
                        <el-radio-group v-model="listForm.type">
                            <el-radio label="ul">无序列表</el-radio>
                            <el-radio label="ol">有序列表</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="列表项">
                        <div
                            v-for="(item, index) in listForm.items"
                            :key="index"
                            class="list-item"
                        >
                            <el-input
                                v-model="listForm.items[index]"
                                placeholder="请输入列表项内容"
                            >
                                <el-button
                                    slot="append"
                                    @click="removeListItem(index)"
                                    icon="el-icon-delete"
                                ></el-button>
                            </el-input>
                        </div>
                        <el-button
                            @click="addListItem"
                            icon="el-icon-plus"
                            size="mini"
                            >添加列表项</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>

            <!-- 商品卡片配置 -->
            <div v-if="currentDialog.type === 'product'">
                <el-form :model="productForm" label-width="100px">
                    <el-form-item label="商品ID">
                        <el-input
                            v-model="productForm.id"
                            placeholder="请输入商品ID"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="商品图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="imageDir"
                            :file-list="productForm.imageList"
                            :limit="1"
                            @change="handleProductImageChange"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="中文标题">
                        <el-input
                            v-model="productForm.titleCn"
                            placeholder="请输入商品中文标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="英文标题">
                        <el-input
                            v-model="productForm.titleEn"
                            placeholder="请输入商品英文标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="价格">
                        <el-input
                            v-model="productForm.price"
                            placeholder="请输入价格"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="已售数量">
                        <el-input
                            v-model="productForm.sold"
                            placeholder="请输入已售数量"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="商品描述">
                        <el-input
                            v-model="productForm.description"
                            type="textarea"
                            placeholder="请输入商品描述"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 引用配置 -->
            <div v-if="currentDialog.type === 'quote'">
                <el-form :model="quoteForm" label-width="80px">
                    <el-form-item label="引用内容">
                        <el-input
                            v-model="quoteForm.content"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入引用内容"
                        >
                        </el-input>
                    </el-form-item>
                    <el-form-item label="引用来源">
                        <el-input
                            v-model="quoteForm.source"
                            placeholder="请输入引用来源（可选）"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 投票配置 -->
            <div v-if="currentDialog.type === 'vote'">
                <el-form :model="voteForm" label-width="100px">
                    <el-form-item label="投票ID">
                        <el-input
                            v-model="voteForm.id"
                            placeholder="请输入投票ID"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="投票标题">
                        <el-input
                            v-model="voteForm.title"
                            placeholder="请输入投票标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="投票类型">
                        <el-radio-group v-model="voteForm.type">
                            <el-radio label="single">单选</el-radio>
                            <el-radio label="multiple">多选</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="截止日期">
                        <el-date-picker
                            v-model="voteForm.deadline"
                            type="date"
                            placeholder="选择截止日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="投票选项">
                        <div
                            v-for="(option, index) in voteForm.options"
                            :key="index"
                            class="vote-option"
                        >
                            <el-row :gutter="10">
                                <el-col :span="10">
                                    <el-input
                                        v-model="option.text"
                                        placeholder="选项文本"
                                    ></el-input>
                                </el-col>
                                <el-col :span="10">
                                    <el-input
                                        v-model="option.value"
                                        placeholder="选项值"
                                    ></el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-button
                                        @click="removeVoteOption(index)"
                                        icon="el-icon-delete"
                                        size="mini"
                                    ></el-button>
                                </el-col>
                            </el-row>
                        </div>
                        <el-button
                            @click="addVoteOption"
                            icon="el-icon-plus"
                            size="mini"
                            >添加选项</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>

            <!-- 表格配置 -->
            <div v-if="currentDialog.type === 'table'">
                <el-form :model="tableForm" label-width="80px">
                    <el-form-item label="行数">
                        <el-input-number
                            v-model="tableForm.rows"
                            :min="2"
                            :max="10"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="列数">
                        <el-input-number
                            v-model="tableForm.cols"
                            :min="2"
                            :max="10"
                            @change="updateTableHeaders"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="表头">
                        <div
                            v-for="(header, index) in tableForm.headers"
                            :key="index"
                            class="table-header"
                        >
                            <el-input
                                v-model="tableForm.headers[index]"
                                :placeholder="`列${index + 1}标题`"
                            ></el-input>
                        </div>
                    </el-form-item>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmInsert"
                    >确定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import vosOss from "vos-oss";

export default {
    name: "MarkdownEditor",
    components: {
        vosOss
    },
    props: {
        value: {
            type: String,
            default: ""
        },
        height: {
            type: Number,
            default: 400
        }
    },
    data() {
        return {
            content: this.value,
            showPreview: false,
            dialogVisible: false,
            imageDir: "vinehoo/news/",

            // 工具栏配置
            toolbarItems: [
                {
                    type: "title",
                    label: "标题",
                    icon: "el-icon-edit-outline",
                    title: "插入标题"
                },
                {
                    type: "subtitle",
                    label: "子标题",
                    icon: "el-icon-edit",
                    title: "插入子标题"
                },
                {
                    type: "date",
                    label: "日期",
                    icon: "el-icon-date",
                    title: "插入日期"
                },
                {
                    type: "textbox",
                    label: "文本",
                    icon: "el-icon-document",
                    title: "插入文本框"
                },
                {
                    type: "image",
                    label: "图片",
                    icon: "el-icon-picture",
                    title: "插入图片"
                },
                {
                    type: "list",
                    label: "列表",
                    icon: "el-icon-menu",
                    title: "插入列表"
                },
                {
                    type: "product",
                    label: "商品",
                    icon: "el-icon-goods",
                    title: "插入商品卡片"
                },
                {
                    type: "quote",
                    label: "引用",
                    icon: "el-icon-chat-line-square",
                    title: "插入引用"
                },
                {
                    type: "vote",
                    label: "投票",
                    icon: "el-icon-s-check",
                    title: "插入投票"
                },
                {
                    type: "table",
                    label: "表格",
                    icon: "el-icon-s-grid",
                    title: "插入表格"
                }
            ],

            // 当前弹窗配置
            currentDialog: {
                type: "",
                title: ""
            },

            // 各种表单数据
            titleForm: { content: "", level: "1" },
            subtitleForm: { content: "" },
            dateForm: { date: "", format: "YYYY-MM-DD" },
            textboxForm: { content: "" },
            imageForm: { fileList: [], alt: "", caption: "", url: "" },
            listForm: { type: "ul", items: [""] },
            productForm: {
                id: "",
                imageList: [],
                titleCn: "",
                titleEn: "",
                price: "",
                sold: "",
                description: "",
                imageUrl: ""
            },
            quoteForm: { content: "", source: "" },
            voteForm: {
                id: "",
                title: "",
                type: "single",
                options: [{ text: "", value: "" }],
                deadline: ""
            },
            tableForm: {
                rows: 3,
                cols: 3,
                headers: [],
                data: []
            }
        };
    },
    computed: {
        previewHtml() {
            // 这里可以添加markdown解析逻辑
            return this.content.replace(/\n/g, "<br>");
        }
    },
    watch: {
        value(newVal) {
            this.content = newVal;
        },
        content(newVal) {
            this.$emit("input", newVal);
        }
    },
    methods: {
        handleInput() {
            this.$emit("input", this.content);
        },

        insertModule(type) {
            const dialogTitles = {
                title: "插入标题",
                subtitle: "插入子标题",
                date: "插入日期",
                textbox: "插入文本框",
                image: "插入图片",
                list: "插入列表",
                product: "插入商品卡片",
                quote: "插入引用",
                vote: "插入投票",
                table: "插入表格"
            };

            this.currentDialog = {
                type: type,
                title: dialogTitles[type]
            };

            // 初始化表单数据
            if (type === "vote") {
                this.voteForm.id = Date.now().toString();
                this.voteForm.options = [{ text: "", value: "" }];
            } else if (type === "table") {
                this.updateTableHeaders();
            }

            this.dialogVisible = true;
        },

        confirmInsert() {
            let insertText = "";

            switch (this.currentDialog.type) {
                case "title":
                    const titlePrefix = "#".repeat(
                        parseInt(this.titleForm.level)
                    );
                    insertText = `\n${titlePrefix} ${this.titleForm.content}\n`;
                    break;

                case "subtitle":
                    insertText = `\n## ${this.subtitleForm.content}\n`;
                    break;

                case "date":
                    insertText = `\n**日期：** ${this.dateForm.date}\n`;
                    break;

                case "textbox":
                    insertText = `\n${this.textboxForm.content}\n`;
                    break;

                case "image":
                    if (this.imageForm.url) {
                        insertText = `\n![${this.imageForm.alt}](${this.imageForm.url} "${this.imageForm.caption}")\n`;
                    }
                    break;

                case "list":
                    const listItems = this.listForm.items.filter(item =>
                        item.trim()
                    );
                    if (listItems.length > 0) {
                        insertText =
                            "\n" +
                            listItems
                                .map((item, index) => {
                                    return this.listForm.type === "ol"
                                        ? `${index + 1}. ${item}`
                                        : `- ${item}`;
                                })
                                .join("\n") +
                            "\n";
                    }
                    break;

                case "product":
                    if (this.productForm.imageUrl) {
                        insertText = `\n:::product-card\n---\nid: ${this.productForm.id}\n---\n![${this.productForm.titleCn}](${this.productForm.imageUrl} "${this.productForm.titleCn}")\n!! ${this.productForm.titleCn}\n!!! ${this.productForm.titleEn}\n**价格：** ${this.productForm.price}\n**已售：** ${this.productForm.sold}\n> ${this.productForm.description}\n:::\n`;
                    }
                    break;

                case "quote":
                    insertText = `\n> ${this.quoteForm.content}`;
                    if (this.quoteForm.source) {
                        insertText += `\n> \n> —— ${this.quoteForm.source}`;
                    }
                    insertText += "\n";
                    break;

                case "vote":
                    const voteOptions = this.voteForm.options.filter(
                        opt => opt.text.trim() && opt.value.trim()
                    );
                    if (voteOptions.length > 0) {
                        const voteType =
                            this.voteForm.type === "multiple"
                                ? ".multiple"
                                : ".single";
                        const deadline = this.voteForm.deadline
                            ? ` .deadline=${this.voteForm.deadline}`
                            : "";
                        insertText = `\n:::vote[${this.voteForm.id}]{${voteType}${deadline}}\n# ${this.voteForm.title}\n\n`;
                        voteOptions.forEach(option => {
                            insertText += `- [ ] ${option.text}{value=${option.value}}\n`;
                        });
                        insertText += ":::\n";
                    }
                    break;

                case "table":
                    if (this.tableForm.headers.length > 0) {
                        // 表头
                        insertText =
                            "\n| " +
                            this.tableForm.headers.join(" | ") +
                            " |\n";
                        // 分隔线
                        insertText +=
                            "|" +
                            this.tableForm.headers
                                .map(() => "-----")
                                .join("|") +
                            "|\n";
                        // 数据行
                        for (let i = 0; i < this.tableForm.rows - 1; i++) {
                            insertText +=
                                "| " +
                                this.tableForm.headers
                                    .map((_, j) => `数据${i + 1}-${j + 1}`)
                                    .join(" | ") +
                                " |\n";
                        }
                    }
                    break;
            }

            if (insertText) {
                this.insertTextAtCursor(insertText);
            }

            this.dialogVisible = false;
            this.resetDialog();
        },

        insertTextAtCursor(text) {
            const textarea = this.$refs.editor.$refs.textarea;
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;

            const before = this.content.substring(0, start);
            const after = this.content.substring(end);

            this.content = before + text + after;

            this.$nextTick(() => {
                textarea.focus();
                textarea.setSelectionRange(
                    start + text.length,
                    start + text.length
                );
            });
        },

        resetDialog() {
            // 重置所有表单数据
            this.titleForm = { content: "", level: "1" };
            this.subtitleForm = { content: "" };
            this.dateForm = { date: "", format: "YYYY-MM-DD" };
            this.textboxForm = { content: "" };
            this.imageForm = { fileList: [], alt: "", caption: "", url: "" };
            this.listForm = { type: "ul", items: [""] };
            this.productForm = {
                id: "",
                imageList: [],
                titleCn: "",
                titleEn: "",
                price: "",
                sold: "",
                description: "",
                imageUrl: ""
            };
            this.quoteForm = { content: "", source: "" };
            this.voteForm = {
                id: "",
                title: "",
                type: "single",
                options: [{ text: "", value: "" }],
                deadline: ""
            };
            this.tableForm = {
                rows: 3,
                cols: 3,
                headers: [],
                data: []
            };
        },

        handleImageChange(fileList) {
            this.imageForm.fileList = fileList;
            if (fileList.length > 0) {
                this.imageForm.url = fileList[0];
            }
        },

        handleProductImageChange(fileList) {
            this.productForm.imageList = fileList;
            if (fileList.length > 0) {
                this.productForm.imageUrl = fileList[0];
            }
        },

        addListItem() {
            this.listForm.items.push("");
        },

        removeListItem(index) {
            if (this.listForm.items.length > 1) {
                this.listForm.items.splice(index, 1);
            }
        },

        togglePreview() {
            this.showPreview = !this.showPreview;
        },

        addVoteOption() {
            this.voteForm.options.push({ text: "", value: "" });
        },

        removeVoteOption(index) {
            if (this.voteForm.options.length > 1) {
                this.voteForm.options.splice(index, 1);
            }
        },

        updateTableHeaders() {
            const cols = this.tableForm.cols || 3;
            this.tableForm.headers = Array(cols)
                .fill("")
                .map((_, index) => `列${index + 1}`);
        }
    }
};
</script>

<style scoped lang="scss">
.markdown-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;

    .editor-toolbar {
        background: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
        padding: 8px 12px;

        .toolbar-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
    }

    .editor-container {
        display: flex;
        min-height: 400px;

        .editor-content {
            flex: 1;

            .el-textarea {
                height: 100%;

                ::v-deep .el-textarea__inner {
                    border: none;
                    border-radius: 0;
                    resize: none;
                    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
                    font-size: 14px;
                    line-height: 1.6;
                }
            }
        }

        .editor-preview {
            flex: 1;
            border-left: 1px solid #dcdfe6;
            background: #fff;

            .preview-content {
                padding: 16px;
                height: 100%;
                overflow-y: auto;
            }
        }
    }
}

.list-item {
    margin-bottom: 8px;
}

.vote-option {
    margin-bottom: 8px;
}

.table-header {
    margin-bottom: 8px;
}

.dialog-footer {
    text-align: right;
}
</style>
