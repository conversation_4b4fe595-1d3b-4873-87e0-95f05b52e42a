// 内容块组件注册
import Vue from 'vue';

// 标题块
Vue.component('TitleBlock', {
  props: ['block'],
  template: `
    <div class="block-title">
      <h1 v-if="block.data.level === '1'">{{ block.data.content }}</h1>
      <h2 v-else-if="block.data.level === '2'">{{ block.data.content }}</h2>
      <h3 v-else>{{ block.data.content }}</h3>
    </div>
  `
});

// 子标题块
Vue.component('SubtitleBlock', {
  props: ['block'],
  template: `
    <div class="block-subtitle">
      <h2>{{ block.data.content }}</h2>
    </div>
  `
});

// 日期块
Vue.component('DateBlock', {
  props: ['block'],
  template: `
    <div class="block-date">
      <strong>日期：</strong>{{ block.data.date }}
    </div>
  `
});

// 文本块
Vue.component('TextBlock', {
  props: ['block'],
  template: `
    <div class="block-textbox" v-html="formattedContent"></div>
  `,
  computed: {
    formattedContent() {
      return this.block.data.content.replace(/\n/g, '<br>');
    }
  }
});

// 图片块
Vue.component('ImageBlock', {
  props: ['block'],
  template: `
    <div class="block-image">
      <img :src="block.data.url" :alt="block.data.alt" />
      <div class="image-caption" v-if="block.data.caption">{{ block.data.caption }}</div>
    </div>
  `
});

// 列表块
Vue.component('ListBlock', {
  props: ['block'],
  template: `
    <div class="block-list">
      <ol>
        <li v-for="(item, index) in block.data.items" :key="index">
          {{ item.text }}
          <ul v-if="item.subItems && item.subItems.length > 0">
            <li v-for="(subItem, subIndex) in item.subItems" :key="subIndex">
              {{ subItem }}
            </li>
          </ul>
        </li>
      </ol>
    </div>
  `
});

// 商品卡片块
Vue.component('ProductBlock', {
  props: ['block'],
  template: `
    <div class="block-product">
      <div class="product-image" v-if="block.data.imageUrl">
        <img :src="block.data.imageUrl" :alt="block.data.titleCn" />
      </div>
      <div class="product-title-cn">{{ block.data.titleCn }}</div>
      <div class="product-title-en" v-if="block.data.titleEn">{{ block.data.titleEn }}</div>
      <div class="product-info">
        <span class="price">价格：{{ block.data.price }}</span>
        <span class="sold">已售：{{ block.data.sold }}</span>
      </div>
      <div class="product-description" v-if="block.data.description">{{ block.data.description }}</div>
    </div>
  `
});

// 引用块
Vue.component('QuoteBlock', {
  props: ['block'],
  template: `
    <div class="block-quote">
      <div>{{ block.data.content }}</div>
      <div class="quote-source" v-if="block.data.source">—— {{ block.data.source }}</div>
    </div>
  `
});

// 投票块
Vue.component('VoteBlock', {
  props: ['block'],
  template: `
    <div class="block-vote">
      <div class="vote-title">{{ block.data.title }}</div>
      <div class="vote-options">
        <div 
          v-for="(option, index) in block.data.options" 
          :key="index"
          class="vote-option-item"
        >
          <input 
            :type="block.data.type === 'multiple' ? 'checkbox' : 'radio'"
            :name="'vote_' + block.data.id"
            :value="option.value"
            disabled
          />
          <span>{{ option.text }}</span>
        </div>
      </div>
      <div class="vote-info">
        <span class="vote-id" v-if="block.data.id">投票ID: {{ block.data.id }}</span>
        <span class="vote-deadline" v-if="block.data.deadline">截止日期: {{ block.data.deadline }}</span>
      </div>
    </div>
  `
});

// 表格块
Vue.component('TableBlock', {
  props: ['block'],
  template: `
    <div class="block-table">
      <table>
        <thead>
          <tr>
            <th v-for="(header, index) in block.data.headers" :key="index">
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in block.data.data" :key="rowIndex">
            <td v-for="(cell, cellIndex) in row" :key="cellIndex">
              {{ cell }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  `
});
