<template>
    <div class="wysiwyg-editor">
        <!-- 工具栏 -->
        <div class="editor-toolbar">
            <div class="toolbar-group">
                <el-button
                    v-for="tool in toolbarItems"
                    :key="tool.type"
                    size="mini"
                    :icon="tool.icon"
                    :title="tool.title"
                    @click="insertModule(tool.type)"
                >
                    {{ tool.label }}
                </el-button>
            </div>
        </div>

        <!-- 可视化编辑区域 -->
        <div class="editor-container" :style="{ minHeight: height + 'px' }">
            <draggable
                v-model="contentBlocks"
                class="content-blocks"
                :options="{ animation: 200, handle: '.drag-handle' }"
                @change="handleBlocksChange"
            >
                <div
                    v-for="(block, index) in contentBlocks"
                    :key="block.id"
                    class="content-block"
                    :class="'block-' + block.type"
                >
                    <!-- 拖拽手柄和操作按钮 -->
                    <div class="block-controls">
                        <i
                            class="el-icon-s-grid drag-handle"
                            title="拖拽排序"
                        ></i>
                        <el-button-group class="block-actions">
                            <el-button
                                size="mini"
                                icon="el-icon-edit"
                                @click="editBlock(block, index)"
                                title="编辑"
                            ></el-button>
                            <el-button
                                size="mini"
                                icon="el-icon-delete"
                                @click="deleteBlock(index)"
                                title="删除"
                            ></el-button>
                        </el-button-group>
                    </div>

                    <!-- 不同类型的内容块渲染 -->
                    <component
                        :is="getBlockComponent(block.type)"
                        :block="block"
                        @edit="editBlock(block, index)"
                    />
                </div>
            </draggable>

            <!-- 空状态提示 -->
            <div v-if="contentBlocks.length === 0" class="empty-state">
                <p>点击上方工具栏添加内容模块</p>
            </div>
        </div>

        <!-- 模块配置弹窗 -->
        <el-dialog
            :title="currentDialog.title"
            :visible.sync="dialogVisible"
            width="600px"
            @close="resetDialog"
        >
            <!-- 标题配置 -->
            <div v-if="currentDialog.type === 'title'">
                <el-form :model="titleForm" label-width="80px">
                    <el-form-item label="标题内容">
                        <el-input
                            v-model="titleForm.content"
                            placeholder="请输入标题内容"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="标题级别">
                        <el-select v-model="titleForm.level">
                            <el-option label="一级标题" value="1"></el-option>
                            <el-option label="二级标题" value="2"></el-option>
                            <el-option label="三级标题" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 子标题配置 -->
            <div v-if="currentDialog.type === 'subtitle'">
                <el-form :model="subtitleForm" label-width="80px">
                    <el-form-item label="子标题内容">
                        <el-input
                            v-model="subtitleForm.content"
                            placeholder="请输入子标题内容"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 日期配置 -->
            <div v-if="currentDialog.type === 'date'">
                <el-form :model="dateForm" label-width="80px">
                    <el-form-item label="选择日期">
                        <el-date-picker
                            v-model="dateForm.date"
                            type="date"
                            placeholder="选择日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="显示格式">
                        <el-select v-model="dateForm.format">
                            <el-option
                                label="2025-01-01"
                                value="YYYY-MM-DD"
                            ></el-option>
                            <el-option
                                label="2025年1月1日"
                                value="YYYY年MM月DD日"
                            ></el-option>
                            <el-option
                                label="January 1, 2025"
                                value="MMMM D, YYYY"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 文本框配置 -->
            <div v-if="currentDialog.type === 'textbox'">
                <el-form :model="textboxForm" label-width="80px">
                    <el-form-item label="文本内容">
                        <el-input
                            v-model="textboxForm.content"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入文本内容"
                        >
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 图片配置 -->
            <div v-if="currentDialog.type === 'image'">
                <el-form :model="imageForm" label-width="80px">
                    <el-form-item label="上传图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="imageDir"
                            :file-list="imageForm.fileList"
                            :limit="1"
                            @change="handleImageChange"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="图片描述">
                        <el-input
                            v-model="imageForm.alt"
                            placeholder="请输入图片描述"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="图片标注">
                        <el-input
                            v-model="imageForm.caption"
                            placeholder="请输入图片标注"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 列表配置 -->
            <div v-if="currentDialog.type === 'list'">
                <el-form :model="listForm" label-width="80px">
                    <el-form-item label="列表项">
                        <div
                            v-for="(item, index) in listForm.items"
                            :key="index"
                            class="list-item-config"
                        >
                            <div class="main-item">
                                <el-input
                                    v-model="item.text"
                                    placeholder="请输入列表项内容"
                                >
                                    <el-button
                                        slot="append"
                                        @click="removeListItem(index)"
                                        icon="el-icon-delete"
                                    ></el-button>
                                </el-input>
                            </div>
                            <!-- 子项配置 -->
                            <div
                                class="sub-items"
                                v-if="item.subItems && item.subItems.length > 0"
                            >
                                <div
                                    v-for="(subItem, subIndex) in item.subItems"
                                    :key="subIndex"
                                    class="sub-item"
                                >
                                    <el-input
                                        v-model="item.subItems[subIndex]"
                                        placeholder="子项内容"
                                        size="small"
                                    >
                                        <el-button
                                            slot="append"
                                            @click="
                                                removeSubItem(index, subIndex)
                                            "
                                            icon="el-icon-delete"
                                            size="small"
                                        ></el-button>
                                    </el-input>
                                </div>
                            </div>
                            <div class="item-actions">
                                <el-button
                                    size="mini"
                                    @click="addSubItem(index)"
                                    icon="el-icon-plus"
                                    >添加子项</el-button
                                >
                            </div>
                        </div>
                        <el-button
                            @click="addListItem"
                            icon="el-icon-plus"
                            size="mini"
                            >添加列表项</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>

            <!-- 商品卡片配置 -->
            <div v-if="currentDialog.type === 'product'">
                <el-form :model="productForm" label-width="100px">
                    <el-form-item label="商品ID">
                        <el-input
                            v-model="productForm.id"
                            placeholder="请输入商品ID"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="商品图片">
                        <vos-oss
                            list-type="picture-card"
                            :showFileList="true"
                            :dir="imageDir"
                            :file-list="productForm.imageList"
                            :limit="1"
                            @change="handleProductImageChange"
                        >
                            <i slot="default" class="el-icon-plus"></i>
                        </vos-oss>
                    </el-form-item>
                    <el-form-item label="中文标题">
                        <el-input
                            v-model="productForm.titleCn"
                            placeholder="请输入商品中文标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="英文标题">
                        <el-input
                            v-model="productForm.titleEn"
                            placeholder="请输入商品英文标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="价格">
                        <el-input
                            v-model="productForm.price"
                            placeholder="请输入价格"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="已售数量">
                        <el-input
                            v-model="productForm.sold"
                            placeholder="请输入已售数量"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="商品描述">
                        <el-input
                            v-model="productForm.description"
                            type="textarea"
                            placeholder="请输入商品描述"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 引用配置 -->
            <div v-if="currentDialog.type === 'quote'">
                <el-form :model="quoteForm" label-width="80px">
                    <el-form-item label="引用内容">
                        <el-input
                            v-model="quoteForm.content"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入引用内容"
                        >
                        </el-input>
                    </el-form-item>
                    <el-form-item label="引用来源">
                        <el-input
                            v-model="quoteForm.source"
                            placeholder="请输入引用来源（可选）"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 投票配置 -->
            <div v-if="currentDialog.type === 'vote'">
                <el-form :model="voteForm" label-width="100px">
                    <el-form-item
                        label="投票ID"
                        v-if="voteForm.id && editingBlockIndex !== -1"
                    >
                        <el-input
                            v-model="voteForm.id"
                            disabled
                            placeholder="投票ID（自动生成）"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="投票标题">
                        <el-input
                            v-model="voteForm.title"
                            placeholder="请输入投票标题"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="投票类型">
                        <el-radio-group v-model="voteForm.type">
                            <el-radio label="single">单选</el-radio>
                            <el-radio label="multiple">多选</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="截止日期">
                        <el-date-picker
                            v-model="voteForm.deadline"
                            type="date"
                            placeholder="选择截止日期"
                            format="yyyy-MM-dd"
                            value-format="yyyy-MM-dd"
                        >
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="投票选项">
                        <div
                            v-for="(option, index) in voteForm.options"
                            :key="index"
                            class="vote-option"
                        >
                            <el-row :gutter="10">
                                <el-col :span="10">
                                    <el-input
                                        v-model="option.text"
                                        placeholder="选项文本"
                                    ></el-input>
                                </el-col>
                                <el-col :span="10">
                                    <el-input
                                        v-model="option.value"
                                        placeholder="选项值"
                                    ></el-input>
                                </el-col>
                                <el-col :span="4">
                                    <el-button
                                        @click="removeVoteOption(index)"
                                        icon="el-icon-delete"
                                        size="mini"
                                    ></el-button>
                                </el-col>
                            </el-row>
                        </div>
                        <el-button
                            @click="addVoteOption"
                            icon="el-icon-plus"
                            size="mini"
                            >添加选项</el-button
                        >
                    </el-form-item>
                </el-form>
            </div>

            <!-- 表格配置 -->
            <div v-if="currentDialog.type === 'table'">
                <el-form :model="tableForm" label-width="80px">
                    <el-form-item label="行数">
                        <el-input-number
                            v-model="tableForm.rows"
                            :min="2"
                            :max="10"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="列数">
                        <el-input-number
                            v-model="tableForm.cols"
                            :min="2"
                            :max="10"
                            @change="updateTableHeaders"
                        ></el-input-number>
                    </el-form-item>
                    <el-form-item label="表头">
                        <div
                            v-for="(header, index) in tableForm.headers"
                            :key="index"
                            class="table-header"
                        >
                            <el-input
                                v-model="tableForm.headers[index]"
                                :placeholder="`列${index + 1}标题`"
                            ></el-input>
                        </div>
                    </el-form-item>
                </el-form>
            </div>

            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmInsert"
                    >确定</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>

<script>
import draggable from "vuedraggable";
import vosOss from "vos-oss";
import "./blocks/index.js"; // 导入内容块组件

export default {
    name: "WysiwygEditor",
    components: {
        draggable,
        vosOss
    },
    props: {
        value: {
            type: String,
            default: ""
        },
        height: {
            type: Number,
            default: 400
        }
    },
    data() {
        return {
            contentBlocks: [],
            dialogVisible: false,
            imageDir: "vinehoo/news/",
            editingBlockIndex: -1, // 当前编辑的块索引，-1表示新增

            // 工具栏配置
            toolbarItems: [
                {
                    type: "title",
                    label: "标题",
                    icon: "el-icon-edit-outline",
                    title: "插入标题"
                },
                {
                    type: "subtitle",
                    label: "子标题",
                    icon: "el-icon-edit",
                    title: "插入子标题"
                },
                {
                    type: "date",
                    label: "日期",
                    icon: "el-icon-date",
                    title: "插入日期"
                },
                {
                    type: "textbox",
                    label: "文本",
                    icon: "el-icon-document",
                    title: "插入文本框"
                },
                {
                    type: "image",
                    label: "图片",
                    icon: "el-icon-picture",
                    title: "插入图片"
                },
                {
                    type: "list",
                    label: "列表",
                    icon: "el-icon-menu",
                    title: "插入列表"
                },
                {
                    type: "product",
                    label: "商品",
                    icon: "el-icon-goods",
                    title: "插入商品卡片"
                },
                {
                    type: "quote",
                    label: "引用",
                    icon: "el-icon-chat-line-square",
                    title: "插入引用"
                },
                {
                    type: "vote",
                    label: "投票",
                    icon: "el-icon-s-check",
                    title: "插入投票"
                },
                {
                    type: "table",
                    label: "表格",
                    icon: "el-icon-s-grid",
                    title: "插入表格"
                }
            ],

            // 当前弹窗配置
            currentDialog: {
                type: "",
                title: ""
            },

            // 各种表单数据
            titleForm: { content: "", level: "1" },
            subtitleForm: { content: "" },
            dateForm: { date: "", format: "YYYY-MM-DD" },
            textboxForm: { content: "" },
            imageForm: { fileList: [], alt: "", caption: "", url: "" },
            listForm: { items: [{ text: "", subItems: [] }] },
            productForm: {
                id: "",
                imageList: [],
                titleCn: "",
                titleEn: "",
                price: "",
                sold: "",
                description: "",
                imageUrl: ""
            },
            quoteForm: { content: "", source: "" },
            voteForm: {
                id: "",
                title: "",
                type: "single",
                options: [{ text: "", value: "" }],
                deadline: ""
            },
            tableForm: {
                rows: 3,
                cols: 3,
                headers: [],
                data: []
            }
        };
    },
    watch: {
        contentBlocks: {
            handler() {
                this.emitMarkdown();
            },
            deep: true
        }
    },
    mounted() {
        // 如果有初始值，解析为内容块
        if (this.value) {
            this.parseMarkdownToBlocks(this.value);
        }
    },
    methods: {
        // 生成唯一ID
        generateId() {
            return (
                "block_" +
                Date.now() +
                "_" +
                Math.random()
                    .toString(36)
                    .substring(2, 11)
            );
        },

        // 获取内容块组件
        getBlockComponent(type) {
            const components = {
                title: "TitleBlock",
                subtitle: "SubtitleBlock",
                date: "DateBlock",
                textbox: "TextBlock",
                image: "ImageBlock",
                list: "ListBlock",
                product: "ProductBlock",
                quote: "QuoteBlock",
                vote: "VoteBlock",
                table: "TableBlock"
            };
            return components[type] || "div";
        },

        // 插入模块
        insertModule(type) {
            const dialogTitles = {
                title: "插入标题",
                subtitle: "插入子标题",
                date: "插入日期",
                textbox: "插入文本框",
                image: "插入图片",
                list: "插入列表",
                product: "插入商品卡片",
                quote: "插入引用",
                vote: "插入投票",
                table: "插入表格"
            };

            this.currentDialog = {
                type: type,
                title: dialogTitles[type]
            };

            this.editingBlockIndex = -1; // 新增模式
            this.resetForms();

            // 初始化表单数据
            if (type === "vote") {
                this.voteForm.options = [{ text: "", value: "" }];
            } else if (type === "table") {
                this.updateTableHeaders();
            } else if (type === "list") {
                this.listForm.items = [{ text: "", subItems: [] }];
            }

            this.dialogVisible = true;
        },

        // 编辑内容块
        editBlock(block, index) {
            this.editingBlockIndex = index;
            this.currentDialog = {
                type: block.type,
                title: "编辑" + this.getBlockTypeName(block.type)
            };

            // 填充表单数据
            this.fillFormData(block);
            this.dialogVisible = true;
        },

        // 获取块类型名称
        getBlockTypeName(type) {
            const names = {
                title: "标题",
                subtitle: "子标题",
                date: "日期",
                textbox: "文本框",
                image: "图片",
                list: "列表",
                product: "商品卡片",
                quote: "引用",
                vote: "投票",
                table: "表格"
            };
            return names[type] || type;
        },

        // 填充表单数据
        fillFormData(block) {
            this.resetForms();

            switch (block.type) {
                case "title":
                    this.titleForm = { ...block.data };
                    break;
                case "subtitle":
                    this.subtitleForm = { ...block.data };
                    break;
                case "date":
                    this.dateForm = { ...block.data };
                    break;
                case "textbox":
                    this.textboxForm = { ...block.data };
                    break;
                case "image":
                    this.imageForm = {
                        ...block.data,
                        fileList: block.data.url ? [block.data.url] : []
                    };
                    break;
                case "list":
                    this.listForm = { ...block.data };
                    break;
                case "product":
                    this.productForm = {
                        ...block.data,
                        imageList: block.data.imageUrl
                            ? [block.data.imageUrl]
                            : []
                    };
                    break;
                case "quote":
                    this.quoteForm = { ...block.data };
                    break;
                case "vote":
                    this.voteForm = { ...block.data };
                    break;
                case "table":
                    this.tableForm = { ...block.data };
                    break;
            }
        },

        // 删除内容块
        deleteBlock(index) {
            this.$confirm("确定要删除这个内容块吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.contentBlocks.splice(index, 1);
                })
                .catch(() => {});
        },

        // 处理块变化
        handleBlocksChange() {
            this.emitMarkdown();
        },

        // 确认插入/编辑
        async confirmInsert() {
            const blockData = this.getBlockData();
            if (!blockData) return;

            // 如果是投票且为新增，需要调用后端接口获取ID
            if (
                this.currentDialog.type === "vote" &&
                this.editingBlockIndex === -1
            ) {
                try {
                    const voteId = await this.createVoteId(blockData);
                    blockData.id = voteId;
                } catch (error) {
                    this.$message.error("创建投票失败");
                    return;
                }
            }

            const block = {
                id: this.generateId(),
                type: this.currentDialog.type,
                data: blockData
            };

            if (this.editingBlockIndex === -1) {
                // 新增
                this.contentBlocks.push(block);
            } else {
                // 编辑
                this.$set(this.contentBlocks, this.editingBlockIndex, {
                    ...this.contentBlocks[this.editingBlockIndex],
                    data: blockData
                });
            }

            this.dialogVisible = false;
            this.resetDialog();
        },

        // 获取块数据
        getBlockData() {
            switch (this.currentDialog.type) {
                case "title":
                    return { ...this.titleForm };
                case "subtitle":
                    return { ...this.subtitleForm };
                case "date":
                    return { ...this.dateForm };
                case "textbox":
                    return { ...this.textboxForm };
                case "image":
                    return {
                        url: this.imageForm.url,
                        alt: this.imageForm.alt,
                        caption: this.imageForm.caption
                    };
                case "list":
                    return {
                        items: this.listForm.items.filter(item =>
                            item.text.trim()
                        )
                    };
                case "product":
                    return {
                        id: this.productForm.id,
                        imageUrl: this.productForm.imageUrl,
                        titleCn: this.productForm.titleCn,
                        titleEn: this.productForm.titleEn,
                        price: this.productForm.price,
                        sold: this.productForm.sold,
                        description: this.productForm.description
                    };
                case "quote":
                    return { ...this.quoteForm };
                case "vote":
                    return {
                        id: this.voteForm.id,
                        title: this.voteForm.title,
                        type: this.voteForm.type,
                        deadline: this.voteForm.deadline,
                        options: this.voteForm.options.filter(
                            opt => opt.text.trim() && opt.value.trim()
                        )
                    };
                case "table":
                    return {
                        rows: this.tableForm.rows,
                        cols: this.tableForm.cols,
                        headers: this.tableForm.headers,
                        data: this.generateTableData()
                    };
                default:
                    return {};
            }
        },

        // 创建投票ID（调用后端接口）
        async createVoteId(voteData) {
            // 这里应该调用实际的后端接口
            // 示例：
            try {
                const response = await this.$request.vote.create({
                    title: voteData.title,
                    type: voteData.type,
                    deadline: voteData.deadline,
                    options: voteData.options
                });
                return response.data.vote_id;
            } catch (error) {
                // 如果接口不存在，返回临时ID
                console.warn("投票创建接口未实现，使用临时ID");
                return "vote_" + Date.now();
            }
        },

        // 重置表单
        resetForms() {
            this.titleForm = { content: "", level: "1" };
            this.subtitleForm = { content: "" };
            this.dateForm = { date: "", format: "YYYY-MM-DD" };
            this.textboxForm = { content: "" };
            this.imageForm = { fileList: [], alt: "", caption: "", url: "" };
            this.listForm = { items: [{ text: "", subItems: [] }] };
            this.productForm = {
                id: "",
                imageList: [],
                titleCn: "",
                titleEn: "",
                price: "",
                sold: "",
                description: "",
                imageUrl: ""
            };
            this.quoteForm = { content: "", source: "" };
            this.voteForm = {
                id: "",
                title: "",
                type: "single",
                options: [{ text: "", value: "" }],
                deadline: ""
            };
            this.tableForm = {
                rows: 3,
                cols: 3,
                headers: [],
                data: []
            };
        },

        // 重置弹窗
        resetDialog() {
            this.currentDialog = { type: "", title: "" };
            this.editingBlockIndex = -1;
            this.resetForms();
        },

        // 图片上传处理
        handleImageChange(fileList) {
            this.imageForm.fileList = fileList;
            if (fileList.length > 0) {
                this.imageForm.url = fileList[0];
            }
        },

        handleProductImageChange(fileList) {
            this.productForm.imageList = fileList;
            if (fileList.length > 0) {
                this.productForm.imageUrl = fileList[0];
            }
        },

        // 列表项操作
        addListItem() {
            this.listForm.items.push({ text: "", subItems: [] });
        },

        removeListItem(index) {
            if (this.listForm.items.length > 1) {
                this.listForm.items.splice(index, 1);
            }
        },

        addSubItem(index) {
            if (!this.listForm.items[index].subItems) {
                this.$set(this.listForm.items[index], "subItems", []);
            }
            this.listForm.items[index].subItems.push("");
        },

        removeSubItem(itemIndex, subIndex) {
            this.listForm.items[itemIndex].subItems.splice(subIndex, 1);
        },

        // 投票选项操作
        addVoteOption() {
            this.voteForm.options.push({ text: "", value: "" });
        },

        removeVoteOption(index) {
            if (this.voteForm.options.length > 1) {
                this.voteForm.options.splice(index, 1);
            }
        },

        // 表格操作
        updateTableHeaders() {
            const cols = this.tableForm.cols || 3;
            this.tableForm.headers = Array(cols)
                .fill("")
                .map((_, index) => `列${index + 1}`);
        },

        generateTableData() {
            const data = [];
            for (let i = 0; i < this.tableForm.rows - 1; i++) {
                const row = [];
                for (let j = 0; j < this.tableForm.cols; j++) {
                    row.push(`数据${i + 1}-${j + 1}`);
                }
                data.push(row);
            }
            return data;
        },

        // 生成Markdown
        emitMarkdown() {
            const markdown = this.blocksToMarkdown(this.contentBlocks);
            this.$emit("input", markdown);
        },

        // 内容块转Markdown
        blocksToMarkdown(blocks) {
            return blocks
                .map(block => this.blockToMarkdown(block))
                .join("\n\n");
        },

        blockToMarkdown(block) {
            switch (block.type) {
                case "title":
                    const prefix = "#".repeat(parseInt(block.data.level));
                    return `${prefix} ${block.data.content}`;
                case "subtitle":
                    return `## ${block.data.content}`;
                case "date":
                    return `**日期：** ${block.data.date}`;
                case "textbox":
                    return block.data.content;
                case "image":
                    return `![${block.data.alt}](${block.data.url} "${block.data.caption}")`;
                case "list":
                    return this.listToMarkdown(block.data.items);
                case "product":
                    return this.productToMarkdown(block.data);
                case "quote":
                    let quote = `> ${block.data.content}`;
                    if (block.data.source) {
                        quote += `\n> \n> —— ${block.data.source}`;
                    }
                    return quote;
                case "vote":
                    return this.voteToMarkdown(block.data);
                case "table":
                    return this.tableToMarkdown(block.data);
                default:
                    return "";
            }
        },

        listToMarkdown(items) {
            return items
                .map((item, index) => {
                    let markdown = `${index + 1}. ${item.text}`;
                    if (item.subItems && item.subItems.length > 0) {
                        const subItems = item.subItems
                            .map(subItem => `   - ${subItem}`)
                            .join("\n");
                        markdown += "\n" + subItems;
                    }
                    return markdown;
                })
                .join("\n");
        },

        productToMarkdown(data) {
            return `:::product-card\n---\nid: ${data.id}\n---\n![${data.titleCn}](${data.imageUrl} "${data.titleCn}")\n!! ${data.titleCn}\n!!! ${data.titleEn}\n**价格：** ${data.price}\n**已售：** ${data.sold}\n> ${data.description}\n:::`;
        },

        voteToMarkdown(data) {
            const voteType = data.type === "multiple" ? ".multiple" : ".single";
            const deadline = data.deadline ? ` .deadline=${data.deadline}` : "";
            let markdown = `:::vote[${data.id}]{${voteType}${deadline}}\n# ${data.title}\n\n`;
            data.options.forEach(option => {
                markdown += `- [ ] ${option.text}{value=${option.value}}\n`;
            });
            markdown += ":::";
            return markdown;
        },

        tableToMarkdown(data) {
            let markdown = "| " + data.headers.join(" | ") + " |\n";
            markdown += "|" + data.headers.map(() => "-----").join("|") + "|\n";
            data.data.forEach(row => {
                markdown += "| " + row.join(" | ") + " |\n";
            });
            return markdown;
        },

        // 解析Markdown为内容块（用于初始化）
        parseMarkdownToBlocks(markdown) {
            // 这里可以实现Markdown解析逻辑
            // 暂时留空，后续可以根据需要实现
            console.log("解析Markdown:", markdown);
        }
    }
};
</script>

<style scoped lang="scss">
.wysiwyg-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background: #fff;

    .editor-toolbar {
        background: #f5f7fa;
        border-bottom: 1px solid #dcdfe6;
        padding: 8px 12px;

        .toolbar-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
    }

    .editor-container {
        padding: 16px;

        .content-blocks {
            min-height: 200px;
        }

        .empty-state {
            text-align: center;
            color: #999;
            padding: 60px 0;

            p {
                margin: 0;
                font-size: 14px;
            }
        }
    }

    .content-block {
        position: relative;
        margin-bottom: 16px;
        padding: 12px;
        border: 1px solid transparent;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);

            .block-controls {
                opacity: 1;
            }
        }

        .block-controls {
            position: absolute;
            top: -8px;
            right: -8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;

            .drag-handle {
                cursor: move;
                color: #999;
                font-size: 16px;
                padding: 4px;
                background: #fff;
                border: 1px solid #ddd;
                border-radius: 4px;

                &:hover {
                    color: #409eff;
                    border-color: #409eff;
                }
            }

            .block-actions {
                background: #fff;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
        }
    }
}

// 表单样式
.list-item-config {
    margin-bottom: 16px;

    .main-item {
        margin-bottom: 8px;
    }

    .sub-items {
        margin-left: 20px;

        .sub-item {
            margin-bottom: 4px;
        }
    }

    .item-actions {
        margin-top: 8px;
        text-align: right;
    }
}

.vote-option {
    margin-bottom: 8px;
}

.table-header {
    margin-bottom: 8px;
}

.dialog-footer {
    text-align: right;
}

// 内容块样式
.block-title {
    h1,
    h2,
    h3 {
        margin: 0;
        color: #333;
    }

    h1 {
        font-size: 24px;
        border-bottom: 2px solid #eee;
        padding-bottom: 8px;
    }

    h2 {
        font-size: 20px;
        color: #666;
    }

    h3 {
        font-size: 16px;
        color: #888;
    }
}

.block-subtitle {
    h2 {
        margin: 0;
        font-size: 18px;
        color: #666;
    }
}

.block-date {
    color: #999;
    font-size: 14px;
}

.block-textbox {
    line-height: 1.6;
    color: #333;
}

.block-image {
    text-align: center;

    img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
    }

    .image-caption {
        margin-top: 8px;
        font-size: 12px;
        color: #999;
        font-style: italic;
    }
}

.block-list {
    ol {
        padding-left: 20px;

        li {
            margin: 8px 0;

            ul {
                margin-top: 4px;
                padding-left: 20px;

                li {
                    margin: 4px 0;
                }
            }
        }
    }
}

.block-product {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 16px;
    background: #fafafa;

    .product-image {
        text-align: center;
        margin-bottom: 12px;

        img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
        }
    }

    .product-title-cn {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 4px;
    }

    .product-title-en {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
    }

    .product-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .price {
            color: #e74c3c;
            font-weight: bold;
        }

        .sold {
            color: #999;
        }
    }

    .product-description {
        font-size: 12px;
        color: #666;
        font-style: italic;
    }
}

.block-quote {
    border-left: 4px solid #ddd;
    margin: 16px 0;
    padding-left: 16px;
    color: #666;
    font-style: italic;

    .quote-source {
        margin-top: 8px;
        text-align: right;
        font-size: 12px;
    }
}

.block-vote {
    border: 1px solid #409eff;
    border-radius: 8px;
    padding: 16px;
    background: #f0f9ff;

    .vote-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
        color: #333;
    }

    .vote-options {
        .vote-option-item {
            margin: 8px 0;
            display: flex;
            align-items: center;

            input[type="checkbox"],
            input[type="radio"] {
                margin-right: 8px;
            }
        }
    }

    .vote-info {
        margin-top: 12px;
        font-size: 12px;
        color: #999;

        .vote-id {
            margin-right: 16px;
        }
    }
}

.block-table {
    overflow-x: auto;

    table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }

        th {
            background: #f5f7fa;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background: #fafafa;
        }
    }
}
</style>
