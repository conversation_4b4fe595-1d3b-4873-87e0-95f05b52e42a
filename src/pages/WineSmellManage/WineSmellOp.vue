<template>
    <div>
        <el-form ref="form" :model="datas" label-width="100px" :rules="rules">
            <el-form-item label="内容类型" prop="cate_id">
                <el-col :span="12">
                    <el-select
                        v-model="datas.cate_id"
                        clearable
                        filterable
                        placeholder="请选择类型"
                        class="filter-item"
                    >
                        <el-option
                            v-for="item in cateOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-col>
            </el-form-item>
            <el-form-item label="所属话题" prop="topic_id">
                <el-select
                    v-model="datas.topic_id"
                    clearable
                    filterable
                    placeholder="请选择话题"
                    class="filter-item"
                >
                    <el-option
                        v-for="item in topicList"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="文章标题" prop="title">
                <el-col :span="12">
                    <el-input
                        v-model="datas.title"
                        placeholder="请输入文章标题"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="副标题" prop="abst">
                <el-col :span="12">
                    <el-input
                        v-model="datas.abst"
                        placeholder="请输入副标题"
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="封面图" prop="img1">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="datas.img1"
                    :limit="1"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <!-- <el-form-item label="热门推荐">
                <el-col :span="12">
                    <el-checkbox v-model="datas.is_hot">热门</el-checkbox>
                    <el-checkbox v-model="datas.is_index">推荐</el-checkbox>
                </el-col>
            </el-form-item> -->

            <el-form-item label="排序" prop="ordid">
                <el-col :span="12">
                    <el-input-number
                        v-model="datas.ordid"
                        placeholder="请输入排序"
                        :precision="0"
                        :step="1"
                        :min="1"
                    ></el-input-number>
                </el-col>
            </el-form-item>
            <el-form-item label="详细内容" prop="info">
                <WysiwygEditor
                    ref="editor"
                    v-model.trim="datas.info"
                    :height="500"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-col :span="12">
                    <el-radio-group v-model="datas.status">
                        <el-radio
                            v-for="(item, key) in statusOptions"
                            :key="key"
                            :label="item.label"
                            >{{ item.value }}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item label="作者/来源">
                <el-col :span="12">
                    <el-input
                        v-model="datas.source"
                        placeholder="请输入作者/来源"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联国家ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.countrys"
                        placeholder="请输入关联国家ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联酒庄ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.villages"
                        placeholder="请输入关联酒庄ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联产区ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.areas"
                        placeholder="请输入关联产区ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联酒款ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.wids"
                        placeholder="请输入关联酒款ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联商品期数" prop="period">
                <el-col :span="12">
                    <el-input
                        v-model="datas.period"
                        placeholder="关联商品ID(多个商品ID中间以英文逗号分割)"
                    />
                </el-col>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
    </div>
</template>
<script>
import WysiwygEditor from "@/components/WysiwygEditor";
import vosOss from "vos-oss";
import { commaSplitNumPattern } from "@/utils/pattern";
export default {
    props: {
        parentObj: Object
    },
    components: {
        vosOss,
        WysiwygEditor
    },
    data() {
        return {
            dir: "vinehoo/news/",

            datas: {
                img1: [],
                img: "",
                title: "",
                // is_hot: "",
                // is_index: "",
                topic_id: "",
                ordid: "",
                topic_name: "",
                info: "# WSET教材错了！卢瓦尔品丽珠就该冰镇喝
——谁说红葡萄酒不能清凉一夏？

## 一、挑战教材：WSET的“温度误区”
传统葡萄酒教育（如WSET教材）常将红葡萄酒的适饮温度笼统定为12-18℃，但这一标准忽视了凉爽产区轻酒体红葡萄酒的独特性。在法国卢瓦尔河谷（LoireValley），品丽珠（CabernetFranc）以其高酸度、低单宁、清新果味的特质，成为冰镇饮用的绝佳候选——甚至可以说，冰镇才是它的灵魂归宿。

## 二、卢瓦尔品丽珠的“冰镇基因”
### 1. 风土赋予的清凉骨架
- 气候与土壤：卢瓦尔河谷属凉爽大陆性气候（如都兰Touraine）或受海洋影响（如南特Nantais），品丽珠成熟缓慢，积累酸度而非糖分。沙质土壤（如圣尼古拉斯-德布尔盖伊Saint-Nicolas-de-Bourgueil）出产的酒款酒体轻盈，单宁细腻如丝
- 典型风味：冰镇后，酒液迸发出酸樱桃、野生草莓、紫罗兰的清新香气，尾调带一丝胡椒与湿石头的矿物质感，完美避开室温下的草本生青味

### 2. 酿造哲学的革新
卢瓦尔酒农深谙品丽珠的特性：
- 减少橡木干预：避免重桶陈掩盖果香，保留鲜活酸度。
- 早采收与轻柔萃取：控制单宁含量，确保酒体轻盈

这种工艺使品丽珠天生适合冰镇——单宁遇冷收缩，反而凸显果味灵动

## 三、冰镇品丽珠的实战指南
1. 适饮温度：10-12℃（高于桃红但低于室温），冷藏45分钟即可
2. 推荐产区与酒款：
   - 索米尔（Saumur）&索米尔-香皮尼（Saumur-Champigny）：酒体最轻盈，覆盆子香气主导，冰镇后如红酒界的“冷萃咖啡”
   - 希农（Chinon）&布尔盖伊（Bourgueil）：沙质土壤酒款优先，避免黏土产区的厚重款
3. 配餐场景：
   - 夏日野餐：搭配鸭肉冷盘、山羊奶酪沙拉；
   - 中式餐桌：解辣川菜火锅，平衡油腻烧烤

> “卢瓦尔品丽珠冰镇后，是红酒与桃红的完美交集——既有红果的深邃，又有白酒的爽冽。”——卢瓦尔酿酒师联盟访谈

:::product-card
![绝无仅有的惊喜口感](https://images.vinehoo.com/vinehoo/goods-images/20250707/1751872561795iwHpwwbNW_NJHQsxNhh.png?w=100&h=100 "绝无仅有的惊喜口感")
# 绝无仅有的惊喜口感
**价格：** ¥349
**已售：** 1567
> Eva去德国挖到的宝藏酒庄
:::

## 四、为何教科书需要“纠偏”？
![葡萄园](https://images.vinehoo.com/vinehoo/tinymce/20250707/1751866677335bbD2c7whc_8ecfSWGh5.png?w=767&h=285 "葡萄园")
WSET教材对红葡萄酒的侍酒温度推荐过于笼统，未细分品种与风土：
- 单宁≠酒体：品丽珠单宁含量虽高于黑比诺，但结构松散，低温不僵化；
- 酸度是核心：卢瓦尔品丽珠酸度达6-7g/L（接近白葡萄酒），支撑冰镇后的平衡感。

全球侍酒师大赛冠军曾直言：“侍酒温度的本质，是放大风土而非遵循教条。” [点我购买](https://www.vinehoo.com) 

## 五、行动呼吁：今夏的“叛逆”体验
Step1：选一支卢瓦尔品丽珠（认准Saumur或Chinon酒标）；
Step2：冷藏至10℃开瓶，杯壁凝霜时斟满；
Step3：感受酸樱桃与湿石在舌尖跳跃——你会明白，卢瓦尔的清凉，正是品丽珠的终极奥义。

> “葡萄酒的规则，永远在风土中书写，不在纸面上固化。”

:::vote[123]{.single .deadline=2025-12-31}
# 你喜欢哪个类型的葡萄酒？
请根据你的喜好，从下面的选项中选择你最喜欢的葡萄酒类型。

- [ ] 干红{value=gh}
- [ ] 干白{value=gb}  
- [ ] 气泡酒{value=qp}
:::

*表：主要国家葡萄酒进口关税政策对比*
| **国家** | **基础关税** | **特殊政策** | **终端加价率** |
|--------------|-------------|-------------|--------------|-
| 印度 | 150% | 邦税叠加 | 300-400% | 
| 埃及 | 1800%(静止酒) | 旅游区免税 | 禁止性壁垒 |
| 越南 | 50% | 2027欧盟零关税 | 70-100% |
| 东盟 | 0% | RCEP协定 | 30-50% |
| 欧盟 | 14-20% | 地理标志互认 | 50-70% |

:::vote[456]{.single .deadline=2025-12-30}
# 你平时选择葡萄酒时最看重以下哪些因素？
请根据你的喜好，从下面的选项中选择你最喜欢的葡萄酒类型。

- [ ] 产地（例如法国、澳大利亚）{value=1}
- [ ] 葡萄品种（如赤霞珠、黑皮诺）{value=2} 
- [ ] 酒的口感（如干型、半甜）{value=3}
- [ ] 价格或性价比 {value=4}
:::",
                abst: "",
                cate_id: "",
                countrys: "",
                villages: "",
                areas: "",
                wids: "",
                period: ""
            },
            datasClone: {
                img1: [],
                img: "",
                title: "",
                topic_id: "",
                // is_hot: "",
                // is_index: "",
                ordid: "",
                topic_name: "",
                info: "",
                abst: "",
                cate_id: "",
                countrys: "",
                villages: "",
                areas: "",
                wids: "",
                period: ""
            },
            hot_indexs: [
                {
                    value: "热门",
                    label: 1
                },
                {
                    value: "推荐",
                    label: 2
                }
            ],
            cateOption: [],
            statusOptions: [
                {
                    label: 1,
                    value: "已上线"
                },
                {
                    label: 0,
                    value: "未上线"
                }
            ],
            rules: {
                status: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "blur"
                    }
                ],
                ordid: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur"
                    }
                ],
                cate_id: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "change"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                // topic_id: [
                //     {
                //         required: true,
                //         message: "请选择话题",
                //         trigger: "blur"
                //     }
                // ],
                abst: [
                    {
                        required: true,
                        message: "请输入副标题",
                        trigger: "blur"
                    }
                ],
                info: [
                    {
                        required: true,
                        message: "请输入详细内容",
                        trigger: "blur"
                    }
                ],
                img1: [
                    {
                        required: true,
                        message: "请上传封面图",
                        trigger: "change"
                    }
                ],
                period: [
                    {
                        required: false,
                        pattern: commaSplitNumPattern,
                        message: "请输入以英文逗号分割的商品ID(无空格)",
                        trigger: "blur"
                    }
                ]
            },
            topicList: []
        };
    },
    mounted() {
        this.getTopicList();
    },
    methods: {
        getTopicName(id) {
            let name = "";
            this.topicList.forEach(item => {
                if (item.id == id) {
                    name = item.title;
                }
            });
            return name;
        },
        //提交
        async getTopicList() {
            const data = {
                type: 0,
                status: 1,
                page: 1,
                limit: 999
            };
            this.$request.winesmel.getTopicList(data).then(res => {
                if (res.data.error_code == 0) {
                    this.topicList = res.data.data.list;
                }
            });
        },
        async submits() {
            if (this.validateForm()) {
                console.log(this.datas);
                this.datas.topic_name = this.getTopicName(this.datas.topic_id);
                this.datas.img = this.datas.img1.join(",");
                this.$request.winesmel
                    .articleOperation(this.datas)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.$emit("close");
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        }
                    });
            }
        },
        async GetType() {
            this.$request.winesmel.getArticleType().then(res => {
                if (res.data.error_code == "0") {
                    this.cateOption = res.data.data.list || [];
                }
            });
        },
        openForm(row) {
            this.GetType();
            if (row) {
                this.$request.winesmel
                    .getArticleInfo({
                        id: row.id
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.datas = res.data.data;
                            this.datas.img1 = this.datas.img.split(",");
                        }
                    });
            }
        },

        clearform() {
            this.datas = this.datasClone;
        },
        singleValidate() {
            let flag = null;
            this.$refs["form"].validateField("info", valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
