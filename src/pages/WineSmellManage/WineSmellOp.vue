<template>
    <div>
        <el-form ref="form" :model="datas" label-width="100px" :rules="rules">
            <el-form-item label="内容类型" prop="cate_id">
                <el-col :span="12">
                    <el-select
                        v-model="datas.cate_id"
                        clearable
                        filterable
                        placeholder="请选择类型"
                        class="filter-item"
                    >
                        <el-option
                            v-for="item in cateOption"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        />
                    </el-select>
                </el-col>
            </el-form-item>
            <el-form-item label="所属话题" prop="topic_id">
                <el-select
                    v-model="datas.topic_id"
                    clearable
                    filterable
                    placeholder="请选择话题"
                    class="filter-item"
                >
                    <el-option
                        v-for="item in topicList"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id"
                    />
                </el-select>
            </el-form-item>
            <el-form-item label="文章标题" prop="title">
                <el-col :span="12">
                    <el-input
                        v-model="datas.title"
                        placeholder="请输入文章标题"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="副标题" prop="abst">
                <el-col :span="12">
                    <el-input
                        v-model="datas.abst"
                        placeholder="请输入副标题"
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="封面图" prop="img1">
                <vos-oss
                    list-type="picture-card"
                    :showFileList="true"
                    :dir="dir"
                    :file-list="datas.img1"
                    :limit="1"
                >
                    <i slot="default" class="el-icon-plus"></i>
                </vos-oss>
            </el-form-item>
            <!-- <el-form-item label="热门推荐">
                <el-col :span="12">
                    <el-checkbox v-model="datas.is_hot">热门</el-checkbox>
                    <el-checkbox v-model="datas.is_index">推荐</el-checkbox>
                </el-col>
            </el-form-item> -->

            <el-form-item label="排序" prop="ordid">
                <el-col :span="12">
                    <el-input-number
                        v-model="datas.ordid"
                        placeholder="请输入排序"
                        :precision="0"
                        :step="1"
                        :min="1"
                    ></el-input-number>
                </el-col>
            </el-form-item>
            <el-form-item label="详细内容" prop="info">
                <MarkdownEditor
                    ref="editor"
                    v-model.trim="datas.info"
                    :height="400"
                />
            </el-form-item>
            <el-form-item label="状态" prop="status">
                <el-col :span="12">
                    <el-radio-group v-model="datas.status">
                        <el-radio
                            v-for="(item, key) in statusOptions"
                            :key="key"
                            :label="item.label"
                            >{{ item.value }}
                        </el-radio>
                    </el-radio-group>
                </el-col>
            </el-form-item>
            <el-form-item label="作者/来源">
                <el-col :span="12">
                    <el-input
                        v-model="datas.source"
                        placeholder="请输入作者/来源"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联国家ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.countrys"
                        placeholder="请输入关联国家ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联酒庄ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.villages"
                        placeholder="请输入关联酒庄ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联产区ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.areas"
                        placeholder="请输入关联产区ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联酒款ID">
                <el-col :span="12">
                    <el-input
                        v-model="datas.wids"
                        placeholder="请输入关联酒款ID"
                    />
                </el-col>
            </el-form-item>
            <el-form-item label="关联商品期数" prop="period">
                <el-col :span="12">
                    <el-input
                        v-model="datas.period"
                        placeholder="关联商品ID(多个商品ID中间以英文逗号分割)"
                    />
                </el-col>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="$emit('close')">取 消</el-button>
            <el-button type="primary" @click="submits">确 定</el-button>
        </div>
    </div>
</template>
<script>
import MarkdownEditor from "@/components/MarkdownEditor";
import vosOss from "vos-oss";
import { commaSplitNumPattern } from "@/utils/pattern";
export default {
    props: {
        parentObj: Object
    },
    components: {
        vosOss,
        MarkdownEditor
    },
    data() {
        return {
            dir: "vinehoo/news/",

            datas: {
                img1: [],
                img: "",
                title: "",
                // is_hot: "",
                // is_index: "",
                topic_id: "",
                ordid: "",
                topic_name: "",
                info: "",
                abst: "",
                cate_id: "",
                countrys: "",
                villages: "",
                areas: "",
                wids: "",
                period: ""
            },
            datasClone: {
                img1: [],
                img: "",
                title: "",
                topic_id: "",
                // is_hot: "",
                // is_index: "",
                ordid: "",
                topic_name: "",
                info: "",
                abst: "",
                cate_id: "",
                countrys: "",
                villages: "",
                areas: "",
                wids: "",
                period: ""
            },
            hot_indexs: [
                {
                    value: "热门",
                    label: 1
                },
                {
                    value: "推荐",
                    label: 2
                }
            ],
            cateOption: [],
            statusOptions: [
                {
                    label: 1,
                    value: "已上线"
                },
                {
                    label: 0,
                    value: "未上线"
                }
            ],
            rules: {
                status: [
                    {
                        required: true,
                        message: "请选择状态",
                        trigger: "blur"
                    }
                ],
                ordid: [
                    {
                        required: true,
                        message: "请输入排序",
                        trigger: "blur"
                    }
                ],
                cate_id: [
                    {
                        required: true,
                        message: "请选择类型",
                        trigger: "change"
                    }
                ],
                title: [
                    {
                        required: true,
                        message: "请输入标题",
                        trigger: "blur"
                    }
                ],
                // topic_id: [
                //     {
                //         required: true,
                //         message: "请选择话题",
                //         trigger: "blur"
                //     }
                // ],
                abst: [
                    {
                        required: true,
                        message: "请输入副标题",
                        trigger: "blur"
                    }
                ],
                info: [
                    {
                        required: true,
                        message: "请输入详细内容",
                        trigger: "blur"
                    }
                ],
                img1: [
                    {
                        required: true,
                        message: "请上传封面图",
                        trigger: "change"
                    }
                ],
                period: [
                    {
                        required: false,
                        pattern: commaSplitNumPattern,
                        message: "请输入以英文逗号分割的商品ID(无空格)",
                        trigger: "blur"
                    }
                ]
            },
            topicList: []
        };
    },
    mounted() {
        this.getTopicList();
    },
    methods: {
        getTopicName(id) {
            let name = "";
            this.topicList.forEach(item => {
                if (item.id == id) {
                    name = item.title;
                }
            });
            return name;
        },
        //提交
        async getTopicList() {
            const data = {
                type: 0,
                status: 1,
                page: 1,
                limit: 999
            };
            this.$request.winesmel.getTopicList(data).then(res => {
                if (res.data.error_code == 0) {
                    this.topicList = res.data.data.list;
                }
            });
        },
        async submits() {
            if (this.validateForm()) {
                console.log(this.datas);
                this.datas.topic_name = this.getTopicName(this.datas.topic_id);
                this.datas.img = this.datas.img1.join(",");
                this.$request.winesmel
                    .articleOperation(this.datas)
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.$emit("close");
                            this.$message({
                                type: "success",
                                message: "操作成功"
                            });
                        }
                    });
            }
        },
        async GetType() {
            this.$request.winesmel.getArticleType().then(res => {
                if (res.data.error_code == "0") {
                    this.cateOption = res.data.data.list || [];
                }
            });
        },
        openForm(row) {
            this.GetType();
            if (row) {
                this.$request.winesmel
                    .getArticleInfo({
                        id: row.id
                    })
                    .then(res => {
                        if (res.data.error_code == 0) {
                            this.datas = res.data.data;
                            this.datas.img1 = this.datas.img.split(",");
                        }
                    });
            }
        },

        clearform() {
            this.datas = this.datasClone;
        },
        singleValidate() {
            let flag = null;
            this.$refs["form"].validateField("info", valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        },
        // 表单验证
        validateForm() {
            let flag = null;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    flag = true;
                } else {
                    flag = false;
                }
            });
            return flag;
        }
    }
};
</script>
<style scoped lang="scss">
.dialog-footer {
    display: flex;
    justify-content: center;
}
</style>
